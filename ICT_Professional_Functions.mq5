//+------------------------------------------------------------------+
//| Professional Dashboard and Visualization Functions              |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Create trading dashboard                                         |
//+------------------------------------------------------------------+
void CreateTradingDashboard() {
    if(dashboardCreated) return;
    
    // Create main dashboard panel
    string objName = "ICT_Dashboard_Panel";
    if(ObjectCreate(chartID, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(chartID, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 30);
        ObjectSetInteger(chartID, objName, OBJPROP_XSIZE, 300);
        ObjectSetInteger(chartID, objName, OBJPROP_YSIZE, 400);
        ObjectSetInteger(chartID, objName, OBJPROP_BGCOLOR, clrDarkSlateGray);
        ObjectSetInteger(chartID, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(chartID, objName, OBJPROP_WIDTH, 2);
    }
    
    // Create dashboard title
    objName = "ICT_Dashboard_Title";
    if(ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0)) {
        ObjectSetInteger(chartID, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 40);
        ObjectSetString(chartID, objName, OBJPROP_TEXT, "ICT MASTER PRO v2.50");
        ObjectSetString(chartID, objName, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, 12);
        ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrGold);
    }
    
    dashboardCreated = true;
    Print("✅ Trading dashboard created successfully");
}

//+------------------------------------------------------------------+
//| Update trading dashboard                                         |
//+------------------------------------------------------------------+
void UpdateTradingDashboard() {
    if(!dashboardCreated) return;
    
    // Update dashboard text
    string text = "";
    text += "═══════════════════════════\n";
    text += "📊 ACCOUNT INFO\n";
    text += "Balance: $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
    text += "Equity: $" + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2) + "\n";
    text += "Free Margin: $" + DoubleToString(AccountInfoDouble(ACCOUNT_FREEMARGIN), 2) + "\n";
    text += "═══════════════════════════\n";
    text += "📈 TRADING STATUS\n";
    text += "Trend Bias: " + (trendBias == 1 ? "🟢 BULLISH" : trendBias == -1 ? "🔴 BEARISH" : "🟡 RANGING") + "\n";
    text += "HTF Bias: " + (htfTrendBias == 1 ? "🟢 BULLISH" : htfTrendBias == -1 ? "🔴 BEARISH" : "🟡 RANGING") + "\n";
    text += "Structure Shift: " + (structureShift ? "✅ YES" : "❌ NO") + "\n";
    text += "Open Trades: " + IntegerToString(CountOpenTrades()) + "\n";
    text += "═══════════════════════════\n";
    text += "📊 PERFORMANCE\n";
    text += "Total Trades: " + IntegerToString(totalTrades) + "\n";
    text += "Win Rate: " + DoubleToString(totalTrades > 0 ? (double)winningTrades/totalTrades*100 : 0, 1) + "%\n";
    text += "Daily Trades: " + IntegerToString(riskMgmt.trades_today) + "/" + IntegerToString(MaxTradesPerDay) + "\n";
    text += "Daily Risk: " + DoubleToString(riskMgmt.daily_risk_used, 1) + "%/" + DoubleToString(MaxRiskPerDay, 1) + "%\n";
    
    // Update dashboard label
    string objName = "ICT_Dashboard_Info";
    if(ObjectFind(chartID, objName) < 0) {
        ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(chartID, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 70);
        ObjectSetString(chartID, objName, OBJPROP_FONT, "Courier New");
        ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrWhite);
    }
    ObjectSetString(chartID, objName, OBJPROP_TEXT, text);
}

//+------------------------------------------------------------------+
//| Enhanced Trade Execution with Professional Features             |
//+------------------------------------------------------------------+
void ExecuteBuyTrade(double entryPrice) {
    if(!ValidateTradeConditions(ORDER_TYPE_BUY)) return;
    
    double lotSize = CalculateOptimalLotSize();
    if(lotSize <= 0) {
        Print("❌ Invalid lot size calculated: ", lotSize);
        return;
    }
    
    // Calculate stop loss and take profit with enhanced logic
    SwingPoint lastLow, dummy1, dummy2, dummy3;
    GetLatestSwingPoints(dummy1, lastLow, dummy2, dummy3);
    
    if(lastLow.price <= 0) {
        Print("❌ No valid swing low found for stop loss");
        return;
    }
    
    double stopLoss = lastLow.price - (StructureBreakPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    double riskPips = (entryPrice - stopLoss) / _Point;
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;
    
    // Enhanced take profit calculation
    double takeProfit = entryPrice + (riskPips * RiskRewardRatio * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    
    // Apply trailing stop distance
    if(UseTrailingStop) {
        double trailingDistance = TrailingStopPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1);
        if(entryPrice - stopLoss < trailingDistance) {
            stopLoss = entryPrice - trailingDistance;
        }
    }
    
    // Validate SL and TP levels
    if(!ValidateStopLevels(ORDER_TYPE_BUY, entryPrice, stopLoss, takeProfit)) {
        Print("❌ Invalid stop levels - Trade rejected");
        return;
    }
    
    // Place the order
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = MaxSlippagePips;
    request.magic = MagicNumber;
    request.comment = "ICT Master Pro - BUY";
    
    if(OrderSend(request, result)) {
        // Update risk management
        double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercent / 100.0;
        riskMgmt.daily_risk_used += RiskPercent;
        riskMgmt.trades_today++;
        totalTrades++;
        lastTradeTime = TimeCurrent();
        
        Print("✅ BUY order executed successfully!");
        Print("📊 Ticket: ", result.order, " | Entry: ", entryPrice, " | SL: ", stopLoss, " | TP: ", takeProfit);
        Print("💰 Lot Size: ", lotSize, " | Risk: $", DoubleToString(riskAmount, 2));
        
        if(EnableAlerts) {
            Alert("ICT Master Pro - BUY Trade Executed at ", DoubleToString(entryPrice, _Digits));
        }
        
        if(SendNotifications) {
            SendNotification("ICT Master Pro: BUY trade opened at " + DoubleToString(entryPrice, _Digits));
        }
    } else {
        Print("❌ Failed to place BUY order. Error: ", GetLastError());
        Print("🔍 Request details - Price: ", entryPrice, " SL: ", stopLoss, " TP: ", takeProfit);
    }
}

//+------------------------------------------------------------------+
//| Enhanced Sell Trade Execution                                   |
//+------------------------------------------------------------------+
void ExecuteSellTrade(double entryPrice) {
    if(!ValidateTradeConditions(ORDER_TYPE_SELL)) return;
    
    double lotSize = CalculateOptimalLotSize();
    if(lotSize <= 0) {
        Print("❌ Invalid lot size calculated: ", lotSize);
        return;
    }
    
    // Calculate stop loss and take profit
    SwingPoint lastHigh, dummy1, dummy2, dummy3;
    GetLatestSwingPoints(lastHigh, dummy1, dummy2, dummy3);
    
    if(lastHigh.price <= 0) {
        Print("❌ No valid swing high found for stop loss");
        return;
    }
    
    double stopLoss = lastHigh.price + (StructureBreakPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    double riskPips = (stopLoss - entryPrice) / _Point;
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;
    
    double takeProfit = entryPrice - (riskPips * RiskRewardRatio * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    
    // Apply trailing stop
    if(UseTrailingStop) {
        double trailingDistance = TrailingStopPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1);
        if(stopLoss - entryPrice < trailingDistance) {
            stopLoss = entryPrice + trailingDistance;
        }
    }
    
    // Validate levels
    if(!ValidateStopLevels(ORDER_TYPE_SELL, entryPrice, stopLoss, takeProfit)) {
        Print("❌ Invalid stop levels - Trade rejected");
        return;
    }
    
    // Place the order
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = MaxSlippagePips;
    request.magic = MagicNumber;
    request.comment = "ICT Master Pro - SELL";
    
    if(OrderSend(request, result)) {
        // Update risk management
        double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercent / 100.0;
        riskMgmt.daily_risk_used += RiskPercent;
        riskMgmt.trades_today++;
        totalTrades++;
        lastTradeTime = TimeCurrent();
        
        Print("✅ SELL order executed successfully!");
        Print("📊 Ticket: ", result.order, " | Entry: ", entryPrice, " | SL: ", stopLoss, " | TP: ", takeProfit);
        Print("💰 Lot Size: ", lotSize, " | Risk: $", DoubleToString(riskAmount, 2));
        
        if(EnableAlerts) {
            Alert("ICT Master Pro - SELL Trade Executed at ", DoubleToString(entryPrice, _Digits));
        }
        
        if(SendNotifications) {
            SendNotification("ICT Master Pro: SELL trade opened at " + DoubleToString(entryPrice, _Digits));
        }
    } else {
        Print("❌ Failed to place SELL order. Error: ", GetLastError());
        Print("🔍 Request details - Price: ", entryPrice, " SL: ", stopLoss, " TP: ", takeProfit);
    }
}

//+------------------------------------------------------------------+
//| Validate trade conditions                                        |
//+------------------------------------------------------------------+
bool ValidateTradeConditions(ENUM_ORDER_TYPE orderType) {
    // Check daily limits
    if(riskMgmt.trades_today >= MaxTradesPerDay) {
        if(EnableDebugMode) Print("❌ Maximum daily trades reached");
        return false;
    }
    
    if(riskMgmt.daily_risk_used + RiskPercent > MaxRiskPerDay) {
        if(EnableDebugMode) Print("❌ Daily risk limit would be exceeded");
        return false;
    }
    
    // Check if we have open trades in same direction
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            if((orderType == ORDER_TYPE_BUY && posType == POSITION_TYPE_BUY) ||
               (orderType == ORDER_TYPE_SELL && posType == POSITION_TYPE_SELL)) {
                if(EnableDebugMode) Print("❌ Already have position in same direction");
                return false;
            }
        }
    }
    
    // Check HTF confirmation if required
    if(RequireHTFConfirmation && UseMultiTimeframe) {
        if(orderType == ORDER_TYPE_BUY && htfTrendBias != 1) {
            if(EnableDebugMode) Print("❌ HTF not bullish for BUY trade");
            return false;
        }
        if(orderType == ORDER_TYPE_SELL && htfTrendBias != -1) {
            if(EnableDebugMode) Print("❌ HTF not bearish for SELL trade");
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size with enhanced risk management        |
//+------------------------------------------------------------------+
double CalculateOptimalLotSize() {
    if(FixedLotSize > 0) return FixedLotSize;
    
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * RiskPercent / 100.0;
    
    // Get recent swing point for risk calculation
    SwingPoint lastHigh, lastLow, dummy1, dummy2;
    GetLatestSwingPoints(lastHigh, lastLow, dummy1, dummy2);
    
    double riskPips = 0;
    if(trendBias == 1 && lastLow.price > 0) {
        riskPips = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - lastLow.price) / _Point;
    } else if(trendBias == -1 && lastHigh.price > 0) {
        riskPips = (lastHigh.price - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    }
    
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;
    if(riskPips <= 0) return 0;
    
    // Add structure break buffer
    riskPips += StructureBreakPips;
    
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotSize = riskAmount / (riskPips * tickValue);
    
    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));
    
    // Additional safety check - don't risk more than 5% on single trade
    double maxRiskAmount = accountBalance * 0.05;
    double maxLotSize = maxRiskAmount / (riskPips * tickValue);
    maxLotSize = MathRound(maxLotSize / lotStep) * lotStep;
    
    lotSize = MathMin(lotSize, maxLotSize);
    
    return lotSize;
}
