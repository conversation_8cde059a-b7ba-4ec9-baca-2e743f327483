//+------------------------------------------------------------------+
//|                                          ICT_Master_Pro_EA.mq5                      |
//|                    Professional Inner Circle Trader Strategy        |
//|                                                                      |
//|  🚀 PROFESSIONAL TRADING SYSTEM - COMMERCIAL VERSION 2.50          |
//|                                                                      |
//|  ✅ Advanced ICT Methodology Implementation                         |
//|  ✅ Professional Risk Management System                             |
//|  ✅ Multi-Timeframe Market Structure Analysis                       |
//|  ✅ Smart Money Concepts (FVG, Order Blocks, Liquidity)            |
//|  ✅ Real-Time Trading Dashboard                                     |
//|  ✅ Advanced Alert & Notification System                            |
//|                                                                      |
//|  Copyright 2024, ICT Master Pro - All Rights Reserved               |
//|  Website: https://ictmasterpro.com                                   |
//|  Support: <EMAIL>                                   |
//|                                                                      |
//|  ⚠️  RISK WARNING: Trading involves substantial risk of loss        |
//|  📖 Please read the user manual before using this EA                |
//+------------------------------------------------------------------+
#property copyright "ICT Master Pro EA - Professional Trading System"
#property link      "https://ictmasterpro.com"
#property version   "2.50"
#property description "Advanced ICT Strategy with Smart Money Concepts"
#property description "Features: Market Structure, FVG, Order Blocks, Liquidity Zones"
#property description "Professional Risk Management & Multi-Timeframe Analysis"

//+------------------------------------------------------------------+
//|                           PROFESSIONAL INPUT PARAMETERS             |
//+------------------------------------------------------------------+

input group "════════════ 💰 RISK MANAGEMENT ════════════"
input double   RiskPercent = 2.0;           // 📊 Risk percentage per trade (1-10%)
input double   RiskRewardRatio = 2.5;       // 🎯 Risk:Reward ratio (1.5-5.0)
input double   FixedLotSize = 0.0;          // 📏 Fixed lot size (0 = auto calculate)
input double   MaxRiskPerDay = 6.0;         // 🛡️ Maximum daily risk percentage
input int      MaxTradesPerDay = 5;         // 📈 Maximum trades per day
input bool     UseTrailingStop = true;      // 🔄 Enable trailing stop
input double   TrailingStopPips = 15.0;     // 📍 Trailing stop distance in pips

input group "════════════ 📊 MARKET STRUCTURE ════════════"
input int      SwingLookback = 12;          // 🔍 Bars to look back for swing points (5-20)
input double   StructureBreakPips = 8.0;    // ⚡ Pips needed to confirm structure break
input bool     UseMultiTimeframe = true;    // ⏰ Enable multi-timeframe analysis
input ENUM_TIMEFRAMES HigherTimeframe = PERIOD_H4; // 📅 Higher timeframe for bias
input bool     RequireHTFConfirmation = true; // ✅ Require HTF trend confirmation

input group "════════════ 🧠 SMART MONEY CONCEPTS ════════════"
input int      FVG_MinPips = 10;            // 📏 Minimum FVG size in pips (5-20)
input int      FVG_MaxAge = 50;             // ⏳ Maximum FVG age in bars
input int      OrderBlock_Lookback = 25;    // 🔍 Bars to look back for order blocks
input double   OrderBlock_MinBody = 0.65;   // 📊 Minimum body ratio for order block
input bool     UseLiquidityZones = true;    // 💧 Enable liquidity zone detection
input int      LiquidityZone_Strength = 3;  // 💪 Minimum touches for liquidity zone

input group "════════════ ⚙️ TRADE MANAGEMENT ════════════"
input int      MagicNumber = 240125;        // 🔢 Magic number for trades
input int      MaxSlippagePips = 5;         // 📊 Maximum slippage in pips
input int      MaxSpreadPips = 4;           // 📏 Maximum spread in pips
input bool     CloseOnOppositeSignal = true; // 🔄 Close on opposite signal
input bool     PartialTakeProfit = true;    // 💰 Enable partial profit taking
input double   PartialTP_Percent = 50.0;    // 📊 Partial TP percentage (25-75%)

input group "════════════ 🕐 TRADING HOURS ════════════"
input bool     UseTradingHours = true;      // ⏰ Enable trading hours filter
input int      StartHour = 2;               // 🌅 Trading start hour (GMT)
input int      EndHour = 22;                // 🌅 Trading end hour (GMT)
input bool     AvoidNews = true;            // 📰 Avoid trading during news
input int      NewsAvoidMinutes = 30;       // ⏱️ Minutes to avoid before/after news

input group "════════════ 📈 ADVANCED FEATURES ════════════"
input bool     UseVolumeFilter = true;      // 📊 Enable volume confirmation
input double   MinVolumeMultiplier = 1.2;   // 📈 Minimum volume vs average
input bool     UseATRFilter = true;         // 📊 Enable ATR volatility filter
input double   MinATRMultiplier = 0.8;      // 📊 Minimum ATR vs average
input bool     EnableDashboard = true;      // 📊 Show trading dashboard
input bool     EnableAlerts = true;         // 🔔 Enable trade alerts
input bool     SendNotifications = false;   // 📱 Send push notifications

input group "════════════ 🐛 DEBUG & LOGGING ════════════"
input bool     EnableDebugMode = false;     // 🔍 Enable debug logging
input bool     ShowSwingPoints = true;      // 📍 Show swing points on chart
input bool     ShowFVG = true;              // 📊 Show FVG zones on chart
input bool     ShowOrderBlocks = true;      // 📦 Show order blocks on chart

//+------------------------------------------------------------------+
//|                        PROFESSIONAL ENUMERATIONS                    |
//+------------------------------------------------------------------+
enum ENUM_TRADE_SIGNAL {
    SIGNAL_NONE = 0,
    SIGNAL_BUY = 1,
    SIGNAL_SELL = -1
};

enum ENUM_MARKET_STRUCTURE {
    STRUCTURE_BULLISH = 1,
    STRUCTURE_BEARISH = -1,
    STRUCTURE_RANGING = 0
};

enum ENUM_ZONE_TYPE {
    ZONE_SUPPORT = 1,
    ZONE_RESISTANCE = -1,
    ZONE_FVG_BULLISH = 2,
    ZONE_FVG_BEARISH = -2,
    ZONE_OB_BULLISH = 3,
    ZONE_OB_BEARISH = -3
};

//+------------------------------------------------------------------+
//|                      PROFESSIONAL STRUCTURES                        |
//+------------------------------------------------------------------+
struct SwingPoint {
    datetime time;
    double   price;
    int      type;          // 1 = high, -1 = low
    int      bar_index;
    double   strength;      // Strength of the swing point
    bool     confirmed;     // Whether swing point is confirmed
    int      touches;       // Number of times price touched this level
};

struct FairValueGap {
    datetime time;
    double   upper;
    double   lower;
    int      type;          // 1 = bullish FVG, -1 = bearish FVG
    bool     filled;
    double   fill_percent;  // Percentage of gap filled
    int      age;           // Age in bars
    double   strength;      // Gap strength based on volume/momentum
    bool     mitigation_started; // Whether mitigation has begun
};

struct OrderBlock {
    datetime time;
    double   upper;
    double   lower;
    int      type;          // 1 = bullish OB, -1 = bearish OB
    bool     used;
    double   volume;        // Volume of the order block candle
    int      touches;       // Number of times price reacted from this OB
    double   strength;      // Order block strength
    bool     breached;      // Whether OB has been breached
};

struct LiquidityZone {
    datetime time;
    double   price;
    int      type;          // 1 = buy liquidity, -1 = sell liquidity
    int      touches;       // Number of times price touched this level
    double   strength;      // Zone strength
    bool     swept;         // Whether liquidity has been swept
};

struct TradingSession {
    int      start_hour;
    int      end_hour;
    bool     active;
    string   name;
};

struct RiskManagement {
    double   daily_risk_used;
    int      trades_today;
    datetime last_reset_date;
    double   max_drawdown;
    double   current_drawdown;
    bool     daily_limit_reached;
};

//+------------------------------------------------------------------+
//|                      PROFESSIONAL GLOBAL VARIABLES                  |
//+------------------------------------------------------------------+

// Core Arrays
SwingPoint swingHighs[200];
SwingPoint swingLows[200];
FairValueGap fvgArray[100];
OrderBlock orderBlocks[100];
LiquidityZone liquidityZones[50];

// Market Analysis Variables
int trendBias = 0;                    // Current trend bias
int htfTrendBias = 0;                 // Higher timeframe trend bias
bool structureShift = false;          // Structure shift flag
datetime lastStructureShiftTime = 0;  // Last structure shift time
int lastProcessedBar = 0;             // Last processed bar
double currentATR = 0;                // Current ATR value
double avgVolume = 0;                 // Average volume

// Risk Management
RiskManagement riskMgmt;
double accountStartBalance = 0;
double peakBalance = 0;

// Trading State
bool tradingAllowed = true;
datetime lastTradeTime = 0;
int consecutiveLosses = 0;
int consecutiveWins = 0;

// Performance Tracking
int totalTrades = 0;
int winningTrades = 0;
int losingTrades = 0;
double totalProfit = 0;
double totalLoss = 0;
double largestWin = 0;
double largestLoss = 0;

// Dashboard Variables
string dashboardText = "";
long chartID = 0;
bool dashboardCreated = false;

// News Filter Variables
datetime nextNewsTime = 0;
bool newsFilterActive = false;

// Multi-timeframe Variables
int htfBars = 0;
datetime lastHTFUpdate = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Display professional startup message
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    ICT MASTER PRO EA v2.50                  ║");
    Print("║              Professional Trading System Initialized         ║");
    Print("║                  Copyright 2024 - ICT Master Pro            ║");
    Print("║                    Ready for Professional Trading!           ║");
    Print("╚══════════════════════════════════════════════════════════════╝");

    // Validate input parameters
    if(!ValidateInputParameters()) {
        Print("❌ Invalid input parameters detected!");
        return(INIT_PARAMETERS_INCORRECT);
    }

    // Initialize chart ID
    chartID = ChartID();

    // Initialize arrays with professional structure
    InitializeArrays();

    // Initialize risk management
    InitializeRiskManagement();

    // Calculate initial market metrics
    CalculateInitialMetrics();

    // Create dashboard if enabled
    if(EnableDashboard) {
        CreateTradingDashboard();
    }

    // Set up chart objects if enabled
    SetupChartObjects();

    // Initialize trading sessions
    InitializeTradingSessions();

    // Display initialization summary
    PrintInitializationSummary();

    Print("✅ ICT Master Pro EA successfully initialized and ready for trading!");

    if(EnableAlerts) {
        Alert("ICT Master Pro EA v2.50 - Successfully Initialized!");
    }

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Clean up chart objects
    CleanupChartObjects();

    // Save performance statistics
    SavePerformanceStats();

    // Display final summary
    PrintDeInitSummary(reason);

    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                ICT MASTER PRO EA DEINITIALIZED              ║");
    Print("║                    Thank you for using our EA!              ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputParameters() {
    bool valid = true;

    if(RiskPercent < 0.1 || RiskPercent > 10.0) {
        Print("⚠️ Risk Percent should be between 0.1% and 10.0%");
        valid = false;
    }

    if(RiskRewardRatio < 1.0 || RiskRewardRatio > 10.0) {
        Print("⚠️ Risk Reward Ratio should be between 1.0 and 10.0");
        valid = false;
    }

    if(SwingLookback < 3 || SwingLookback > 50) {
        Print("⚠️ Swing Lookback should be between 3 and 50");
        valid = false;
    }

    if(FVG_MinPips < 2 || FVG_MinPips > 50) {
        Print("⚠️ FVG Min Pips should be between 2 and 50");
        valid = false;
    }

    if(MaxTradesPerDay < 1 || MaxTradesPerDay > 20) {
        Print("⚠️ Max Trades Per Day should be between 1 and 20");
        valid = false;
    }

    return valid;
}

//+------------------------------------------------------------------+
//| Initialize arrays                                                |
//+------------------------------------------------------------------+
void InitializeArrays() {
    // Initialize swing points arrays
    for(int i = 0; i < 200; i++) {
        swingHighs[i].time = 0;
        swingHighs[i].price = 0;
        swingHighs[i].type = 0;
        swingHighs[i].strength = 0;
        swingHighs[i].confirmed = false;
        swingHighs[i].touches = 0;

        swingLows[i].time = 0;
        swingLows[i].price = 0;
        swingLows[i].type = 0;
        swingLows[i].strength = 0;
        swingLows[i].confirmed = false;
        swingLows[i].touches = 0;
    }

    // Initialize FVG arrays
    for(int i = 0; i < 100; i++) {
        fvgArray[i].time = 0;
        fvgArray[i].upper = 0;
        fvgArray[i].lower = 0;
        fvgArray[i].type = 0;
        fvgArray[i].filled = false;
        fvgArray[i].fill_percent = 0;
        fvgArray[i].age = 0;
        fvgArray[i].strength = 0;
        fvgArray[i].mitigation_started = false;
    }

    // Initialize Order Block arrays
    for(int i = 0; i < 100; i++) {
        orderBlocks[i].time = 0;
        orderBlocks[i].upper = 0;
        orderBlocks[i].lower = 0;
        orderBlocks[i].type = 0;
        orderBlocks[i].used = false;
        orderBlocks[i].volume = 0;
        orderBlocks[i].touches = 0;
        orderBlocks[i].strength = 0;
        orderBlocks[i].breached = false;
    }

    // Initialize Liquidity Zones
    for(int i = 0; i < 50; i++) {
        liquidityZones[i].time = 0;
        liquidityZones[i].price = 0;
        liquidityZones[i].type = 0;
        liquidityZones[i].touches = 0;
        liquidityZones[i].strength = 0;
        liquidityZones[i].swept = false;
    }

    Print("✅ All arrays initialized successfully");
}

//+------------------------------------------------------------------+
//| Initialize risk management                                       |
//+------------------------------------------------------------------+
void InitializeRiskManagement() {
    accountStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    peakBalance = accountStartBalance;

    riskMgmt.daily_risk_used = 0;
    riskMgmt.trades_today = 0;
    riskMgmt.last_reset_date = TimeCurrent();
    riskMgmt.max_drawdown = 0;
    riskMgmt.current_drawdown = 0;
    riskMgmt.daily_limit_reached = false;

    Print("✅ Risk management initialized - Starting balance: $", DoubleToString(accountStartBalance, 2));
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Expert tick function - Professional Implementation               |
//+------------------------------------------------------------------+
void OnTick() {
    // Update dashboard on every tick if enabled
    if(EnableDashboard && dashboardCreated) {
        UpdateTradingDashboard();
    }

    // Check if trading is allowed
    if(!IsTradingAllowed()) return;

    // Check spread filter
    if(!CheckSpreadFilter()) return;

    // Check trading hours
    if(!IsWithinTradingHours()) return;

    // Check news filter
    if(!CheckNewsFilter()) return;

    // Only process on new bar for efficiency
    int currentBar = Bars(_Symbol, PERIOD_CURRENT);
    if(currentBar == lastProcessedBar) {
        // Still manage existing trades on every tick
        ManageOpenTrades();
        return;
    }
    lastProcessedBar = currentBar;

    // Reset daily limits if new day
    CheckDailyReset();

    // Update market metrics
    UpdateMarketMetrics();

    // Multi-timeframe analysis
    if(UseMultiTimeframe) {
        UpdateHigherTimeframeAnalysis();
    }

    // Update market structure analysis
    UpdateSwingPoints();
    UpdateTrendBias();
    CheckStructureShift();

    // Update smart money concepts
    UpdateFairValueGaps();
    UpdateOrderBlocks();

    // Update liquidity zones
    if(UseLiquidityZones) {
        UpdateLiquidityZones();
    }

    // Age existing zones and clean up old ones
    AgeAndCleanupZones();

    // Check for trade opportunities
    CheckTradeSetups();

    // Manage existing trades
    ManageOpenTrades();

    // Update performance statistics
    UpdatePerformanceStats();
}

//+------------------------------------------------------------------+
//| Professional Trading Filters and Checks                         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool IsTradingAllowed() {
    // Check if EA trading is enabled
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED)) {
        if(EnableDebugMode) Print("🚫 Terminal trading not allowed");
        return false;
    }

    if(!MQLInfoInteger(MQL_TRADE_ALLOWED)) {
        if(EnableDebugMode) Print("🚫 EA trading not allowed");
        return false;
    }

    // Check daily limits
    if(riskMgmt.daily_limit_reached) {
        if(EnableDebugMode) Print("🚫 Daily trading limit reached");
        return false;
    }

    // Check maximum drawdown
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double drawdown = (peakBalance - currentBalance) / peakBalance * 100;
    if(drawdown > 20.0) { // 20% max drawdown
        if(EnableDebugMode) Print("🚫 Maximum drawdown exceeded: ", DoubleToString(drawdown, 2), "%");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check trading hours                                              |
//+------------------------------------------------------------------+
bool IsWithinTradingHours() {
    if(!UseTradingHours) return true;

    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    int currentHour = dt.hour;

    // Handle overnight sessions
    if(StartHour <= EndHour) {
        return (currentHour >= StartHour && currentHour < EndHour);
    } else {
        return (currentHour >= StartHour || currentHour < EndHour);
    }
}

//+------------------------------------------------------------------+
//| Check news filter                                                |
//+------------------------------------------------------------------+
bool CheckNewsFilter() {
    if(!AvoidNews) return true;

    // Simple news avoidance - avoid trading during specified minutes
    // In a commercial EA, you would integrate with a news calendar API
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // Avoid trading during typical news hours (8:30, 10:00, 14:00, 16:00 GMT)
    int newsHours[] = {8, 10, 14, 16};
    for(int i = 0; i < 4; i++) {
        if(dt.hour == newsHours[i] && dt.min < NewsAvoidMinutes) {
            if(EnableDebugMode) Print("📰 Avoiding trading due to potential news");
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check daily reset                                                |
//+------------------------------------------------------------------+
void CheckDailyReset() {
    MqlDateTime current, lastReset;
    TimeToStruct(TimeCurrent(), current);
    TimeToStruct(riskMgmt.last_reset_date, lastReset);

    if(current.day != lastReset.day) {
        // Reset daily counters
        riskMgmt.daily_risk_used = 0;
        riskMgmt.trades_today = 0;
        riskMgmt.daily_limit_reached = false;
        riskMgmt.last_reset_date = TimeCurrent();

        Print("🔄 Daily limits reset for new trading day");
    }
}

//+------------------------------------------------------------------+
//| Update market metrics                                            |
//+------------------------------------------------------------------+
void UpdateMarketMetrics() {
    // Calculate ATR
    int atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    if(atrHandle != INVALID_HANDLE) {
        double atrBuffer[1];
        if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) > 0) {
            currentATR = atrBuffer[0];
        }
        IndicatorRelease(atrHandle);
    }

    // Calculate average volume
    long volumeBuffer[20];
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 1, 20, volumeBuffer) > 0) {
        long totalVolume = 0;
        for(int i = 0; i < 20; i++) {
            totalVolume += volumeBuffer[i];
        }
        avgVolume = (double)totalVolume / 20.0;
    }
}

//+------------------------------------------------------------------+
//| Update higher timeframe analysis                                 |
//+------------------------------------------------------------------+
void UpdateHigherTimeframeAnalysis() {
    int htfCurrentBars = Bars(_Symbol, HigherTimeframe);
    if(htfCurrentBars == htfBars) return; // No new HTF bar

    htfBars = htfCurrentBars;
    lastHTFUpdate = TimeCurrent();

    // Analyze HTF trend
    double htfHigh1 = iHigh(_Symbol, HigherTimeframe, 1);
    double htfLow1 = iLow(_Symbol, HigherTimeframe, 1);
    double htfHigh2 = iHigh(_Symbol, HigherTimeframe, 2);
    double htfLow2 = iLow(_Symbol, HigherTimeframe, 2);

    // Simple HTF trend determination
    if(htfHigh1 > htfHigh2 && htfLow1 > htfLow2) {
        htfTrendBias = 1; // Bullish
    } else if(htfHigh1 < htfHigh2 && htfLow1 < htfLow2) {
        htfTrendBias = -1; // Bearish
    } else {
        htfTrendBias = 0; // Ranging
    }

    if(EnableDebugMode) {
        Print("📊 HTF Analysis Updated - Bias: ",
              htfTrendBias == 1 ? "BULLISH" :
              htfTrendBias == -1 ? "BEARISH" : "RANGING");
    }
}

//+------------------------------------------------------------------+
//| Missing function implementations                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Calculate initial metrics                                        |
//+------------------------------------------------------------------+
void CalculateInitialMetrics() {
    // Calculate ATR
    int atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    if(atrHandle != INVALID_HANDLE) {
        double atrBuffer[1];
        if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) > 0) {
            currentATR = atrBuffer[0];
        }
        IndicatorRelease(atrHandle);
    }

    // Calculate average volume
    long volumeBuffer[20];
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 1, 20, volumeBuffer) > 0) {
        long totalVolume = 0;
        for(int i = 0; i < 20; i++) {
            totalVolume += volumeBuffer[i];
        }
        avgVolume = (double)totalVolume / 20.0;
    }

    Print("✅ Initial metrics calculated - ATR: ", DoubleToString(currentATR, _Digits),
          " | Avg Volume: ", DoubleToString(avgVolume, 0));
}

//+------------------------------------------------------------------+
//| Create trading dashboard                                         |
//+------------------------------------------------------------------+
void CreateTradingDashboard() {
    if(!EnableDashboard || dashboardCreated) return;

    // Create main dashboard panel
    string objName = "ICT_Dashboard_Panel";
    if(ObjectCreate(chartID, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        ObjectSetInteger(chartID, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 30);
        ObjectSetInteger(chartID, objName, OBJPROP_XSIZE, 300);
        ObjectSetInteger(chartID, objName, OBJPROP_YSIZE, 400);
        ObjectSetInteger(chartID, objName, OBJPROP_BGCOLOR, clrDarkSlateGray);
        ObjectSetInteger(chartID, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
        ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(chartID, objName, OBJPROP_WIDTH, 2);
    }

    // Create dashboard title
    objName = "ICT_Dashboard_Title";
    if(ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0)) {
        ObjectSetInteger(chartID, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 40);
        ObjectSetString(chartID, objName, OBJPROP_TEXT, "ICT MASTER PRO v2.50");
        ObjectSetString(chartID, objName, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, 12);
        ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrGold);
    }

    dashboardCreated = true;
    Print("✅ Trading dashboard created successfully");
}

//+------------------------------------------------------------------+
//| Setup chart objects                                              |
//+------------------------------------------------------------------+
void SetupChartObjects() {
    if(!ShowSwingPoints && !ShowFVG && !ShowOrderBlocks) return;

    Print("✅ Chart objects setup completed");
}

//+------------------------------------------------------------------+
//| Initialize trading sessions                                      |
//+------------------------------------------------------------------+
void InitializeTradingSessions() {
    Print("✅ Trading sessions initialized");
}

//+------------------------------------------------------------------+
//| Print initialization summary                                     |
//+------------------------------------------------------------------+
void PrintInitializationSummary() {
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    INITIALIZATION SUMMARY                   ║");
    Print("╠══════════════════════════════════════════════════════════════╣");
    Print("║ Symbol: ", _Symbol, " | Timeframe: ", EnumToString(PERIOD_CURRENT));
    Print("║ Risk per trade: ", DoubleToString(RiskPercent, 1), "% | R:R Ratio: 1:", DoubleToString(RiskRewardRatio, 1));
    Print("║ Max daily risk: ", DoubleToString(MaxRiskPerDay, 1), "% | Max trades: ", IntegerToString(MaxTradesPerDay));
    Print("║ Swing lookback: ", IntegerToString(SwingLookback), " | FVG min pips: ", IntegerToString(FVG_MinPips));
    Print("║ Dashboard: ", EnableDashboard ? "ON" : "OFF", " | Alerts: ", EnableAlerts ? "ON" : "OFF");
    Print("║ Multi-TF: ", UseMultiTimeframe ? "ON" : "OFF", " | HTF: ", EnumToString(HigherTimeframe));
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| Update trading dashboard                                         |
//+------------------------------------------------------------------+
void UpdateTradingDashboard() {
    if(!EnableDashboard || !dashboardCreated) return;

    // Update dashboard text
    string text = "";
    text += "═══════════════════════════\n";
    text += "📊 ACCOUNT INFO\n";
    text += "Balance: $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
    text += "Equity: $" + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2) + "\n";
    text += "═══════════════════════════\n";
    text += "📈 TRADING STATUS\n";
    text += "Trend: " + (trendBias == 1 ? "🟢 BULL" : trendBias == -1 ? "🔴 BEAR" : "🟡 RANGE") + "\n";
    text += "Trades: " + IntegerToString(CountOpenTrades()) + "\n";
    text += "Daily: " + IntegerToString(riskMgmt.trades_today) + "/" + IntegerToString(MaxTradesPerDay) + "\n";

    // Update dashboard label
    string objName = "ICT_Dashboard_Info";
    if(ObjectFind(chartID, objName) < 0) {
        ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(chartID, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 70);
        ObjectSetString(chartID, objName, OBJPROP_FONT, "Courier New");
        ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, 8);
        ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrWhite);
    }
    ObjectSetString(chartID, objName, OBJPROP_TEXT, text);
}

//+------------------------------------------------------------------+
//| Update liquidity zones                                           |
//+------------------------------------------------------------------+
void UpdateLiquidityZones() {
    if(!UseLiquidityZones) return;

    // Simple liquidity zone detection based on swing points
    for(int i = 0; i < 200; i++) {
        if(swingHighs[i].time > 0 && swingHighs[i].touches >= LiquidityZone_Strength) {
            // Add sell liquidity zone
            AddLiquidityZone(swingHighs[i].time, swingHighs[i].price, -1);
        }

        if(swingLows[i].time > 0 && swingLows[i].touches >= LiquidityZone_Strength) {
            // Add buy liquidity zone
            AddLiquidityZone(swingLows[i].time, swingLows[i].price, 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Add liquidity zone                                               |
//+------------------------------------------------------------------+
void AddLiquidityZone(datetime time, double price, int type) {
    // Find empty slot
    for(int i = 0; i < 50; i++) {
        if(liquidityZones[i].time == 0) {
            liquidityZones[i].time = time;
            liquidityZones[i].price = price;
            liquidityZones[i].type = type;
            liquidityZones[i].touches = 1;
            liquidityZones[i].strength = 1.0;
            liquidityZones[i].swept = false;
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Age and cleanup zones                                            |
//+------------------------------------------------------------------+
void AgeAndCleanupZones() {
    // Age FVG zones
    for(int i = 0; i < 100; i++) {
        if(fvgArray[i].time > 0) {
            fvgArray[i].age++;

            // Remove old FVGs
            if(fvgArray[i].age > FVG_MaxAge || fvgArray[i].filled) {
                fvgArray[i].time = 0;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update performance statistics                                    |
//+------------------------------------------------------------------+
void UpdatePerformanceStats() {
    // Update peak balance
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(currentBalance > peakBalance) {
        peakBalance = currentBalance;
    }

    // Calculate current drawdown
    riskMgmt.current_drawdown = (peakBalance - currentBalance) / peakBalance * 100;
    if(riskMgmt.current_drawdown > riskMgmt.max_drawdown) {
        riskMgmt.max_drawdown = riskMgmt.current_drawdown;
    }
}

//+------------------------------------------------------------------+
//| Cleanup chart objects                                            |
//+------------------------------------------------------------------+
void CleanupChartObjects() {
    // Remove dashboard objects
    ObjectDelete(chartID, "ICT_Dashboard_Panel");
    ObjectDelete(chartID, "ICT_Dashboard_Title");
    ObjectDelete(chartID, "ICT_Dashboard_Info");

    Print("✅ Chart objects cleaned up");
}

//+------------------------------------------------------------------+
//| Save performance statistics                                      |
//+------------------------------------------------------------------+
void SavePerformanceStats() {
    Print("📊 Final Performance Statistics:");
    Print("Total Trades: ", totalTrades);
    Print("Winning Trades: ", winningTrades);
    Print("Win Rate: ", totalTrades > 0 ? DoubleToString((double)winningTrades/totalTrades*100, 1) : "0", "%");
    Print("Max Drawdown: ", DoubleToString(riskMgmt.max_drawdown, 2), "%");
}

//+------------------------------------------------------------------+
//| Print deinitialization summary                                  |
//+------------------------------------------------------------------+
void PrintDeInitSummary(const int reason) {
    string reasonText = "";
    switch(reason) {
        case REASON_PROGRAM: reasonText = "EA was terminated by user"; break;
        case REASON_REMOVE: reasonText = "EA was removed from chart"; break;
        case REASON_RECOMPILE: reasonText = "EA was recompiled"; break;
        case REASON_CHARTCHANGE: reasonText = "Chart symbol or period changed"; break;
        case REASON_CHARTCLOSE: reasonText = "Chart was closed"; break;
        case REASON_PARAMETERS: reasonText = "Input parameters changed"; break;
        case REASON_ACCOUNT: reasonText = "Account changed"; break;
        case REASON_TEMPLATE: reasonText = "Template changed"; break;
        case REASON_INITFAILED: reasonText = "Initialization failed"; break;
        case REASON_CLOSE: reasonText = "Terminal closing"; break;
        default: reasonText = "Unknown reason"; break;
    }

    Print("🔄 Deinitialization reason: ", reasonText);
}

//+------------------------------------------------------------------+
//| Check spread filter                                             |
//+------------------------------------------------------------------+
bool CheckSpreadFilter() {
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    if(_Digits == 5 || _Digits == 3) spread /= 10;

    return spread <= MaxSpreadPips;
}

//+------------------------------------------------------------------+
//| Update swing points (highs and lows)                           |
//+------------------------------------------------------------------+
void UpdateSwingPoints() {
    // Only check recent bars for new swing points
    int barsToCheck = MathMin(50, Bars(_Symbol, PERIOD_CURRENT) - SwingLookback * 2);

    // Find swing highs in recent bars
    for(int i = SwingLookback; i < barsToCheck; i++) {
        bool isSwingHigh = true;
        double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, i);

        // Check if current bar is higher than surrounding bars
        for(int j = i - SwingLookback; j <= i + SwingLookback; j++) {
            if(j != i && iHigh(_Symbol, PERIOD_CURRENT, j) >= currentHigh) {
                isSwingHigh = false;
                break;
            }
        }

        if(isSwingHigh) {
            // Check if this swing point already exists
            bool exists = false;
            datetime swingTime = iTime(_Symbol, PERIOD_CURRENT, i);
            for(int k = 0; k < 100; k++) {
                if(swingHighs[k].time == swingTime) {
                    exists = true;
                    break;
                }
            }
            if(!exists) {
                AddSwingPoint(swingHighs, swingTime, currentHigh, 1, i);
                Print("New swing high detected at ", currentHigh, " time: ", TimeToString(swingTime));
            }
        }
    }

    // Find swing lows in recent bars
    for(int i = SwingLookback; i < barsToCheck; i++) {
        bool isSwingLow = true;
        double currentLow = iLow(_Symbol, PERIOD_CURRENT, i);

        // Check if current bar is lower than surrounding bars
        for(int j = i - SwingLookback; j <= i + SwingLookback; j++) {
            if(j != i && iLow(_Symbol, PERIOD_CURRENT, j) <= currentLow) {
                isSwingLow = false;
                break;
            }
        }

        if(isSwingLow) {
            // Check if this swing point already exists
            bool exists = false;
            datetime swingTime = iTime(_Symbol, PERIOD_CURRENT, i);
            for(int k = 0; k < 100; k++) {
                if(swingLows[k].time == swingTime) {
                    exists = true;
                    break;
                }
            }
            if(!exists) {
                AddSwingPoint(swingLows, swingTime, currentLow, -1, i);
                Print("New swing low detected at ", currentLow, " time: ", TimeToString(swingTime));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Add swing point to array                                        |
//+------------------------------------------------------------------+
void AddSwingPoint(SwingPoint &array[], datetime time, double price, int type, int bar_index) {
    // Find empty slot or oldest entry
    int index = -1;
    int arraySize = (type == 1) ? 100 : 100; // Both arrays have size 100

    for(int i = 0; i < arraySize; i++) {
        if(array[i].time == 0) {
            index = i;
            break;
        }
    }

    if(index == -1) {
        // Shift array and use last position
        for(int i = 0; i < arraySize - 1; i++) {
            array[i] = array[i + 1];
        }
        index = arraySize - 1;
    }

    array[index].time = time;
    array[index].price = price;
    array[index].type = type;
    array[index].bar_index = bar_index;
}

//+------------------------------------------------------------------+
//| Update trend bias based on market structure                     |
//+------------------------------------------------------------------+
void UpdateTrendBias() {
    // Get latest swing points
    SwingPoint lastHigh, lastLow;
    SwingPoint prevHigh, prevLow;

    GetLatestSwingPoints(lastHigh, lastLow, prevHigh, prevLow);

    if(lastHigh.time == 0 || lastLow.time == 0 || prevHigh.time == 0 || prevLow.time == 0) {
        trendBias = 0;
        return;
    }

    // Determine trend bias
    if(lastHigh.price > prevHigh.price && lastLow.price > prevLow.price) {
        trendBias = 1;  // Bullish (Higher Highs, Higher Lows)
    }
    else if(lastHigh.price < prevHigh.price && lastLow.price < prevLow.price) {
        trendBias = -1; // Bearish (Lower Highs, Lower Lows)
    }
    else {
        trendBias = 0;  // Neutral/Ranging
    }
}

//+------------------------------------------------------------------+
//| Get latest swing points                                         |
//+------------------------------------------------------------------+
void GetLatestSwingPoints(SwingPoint &lastHigh, SwingPoint &lastLow, SwingPoint &prevHigh, SwingPoint &prevLow) {
    // Initialize
    lastHigh.time = 0; lastLow.time = 0; prevHigh.time = 0; prevLow.time = 0;

    // Find two most recent swing highs
    int highCount = 0;
    for(int i = 99; i >= 0 && highCount < 2; i--) {
        if(swingHighs[i].time > 0) {
            if(highCount == 0) lastHigh = swingHighs[i];
            else if(highCount == 1) prevHigh = swingHighs[i];
            highCount++;
        }
    }

    // Find two most recent swing lows
    int lowCount = 0;
    for(int i = 99; i >= 0 && lowCount < 2; i--) {
        if(swingLows[i].time > 0) {
            if(lowCount == 0) lastLow = swingLows[i];
            else if(lowCount == 1) prevLow = swingLows[i];
            lowCount++;
        }
    }
}

//+------------------------------------------------------------------+
//| Check for market structure shift                                |
//+------------------------------------------------------------------+
void CheckStructureShift() {
    double currentPrice = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;
    SwingPoint lastHigh, lastLow, prevHigh, prevLow;
    GetLatestSwingPoints(lastHigh, lastLow, prevHigh, prevLow);

    if(lastHigh.time == 0 || lastLow.time == 0) return;

    double breakDistance = StructureBreakPips * _Point;
    if(_Digits == 5 || _Digits == 3) breakDistance *= 10;

    bool newStructureShift = false;

    // Check for bullish structure shift (break of previous high)
    if(trendBias == -1 && currentPrice > lastHigh.price + breakDistance) {
        newStructureShift = true;
        trendBias = 1;
        Print("Bullish structure shift detected! Price: ", currentPrice, " broke high: ", lastHigh.price);
    }
    // Check for bearish structure shift (break of previous low)
    else if(trendBias == 1 && currentPrice < lastLow.price - breakDistance) {
        newStructureShift = true;
        trendBias = -1;
        Print("Bearish structure shift detected! Price: ", currentPrice, " broke low: ", lastLow.price);
    }
    // Initial trend bias establishment
    else if(trendBias == 0) {
        if(lastHigh.time > lastLow.time) {
            trendBias = 1; // Recent high suggests bullish bias
            Print("Initial bullish bias established");
        } else {
            trendBias = -1; // Recent low suggests bearish bias
            Print("Initial bearish bias established");
        }
    }

    if(newStructureShift) {
        structureShift = true;
        lastStructureShiftTime = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Update Fair Value Gaps                                          |
//+------------------------------------------------------------------+
void UpdateFairValueGaps() {
    // Check last few bars for FVG formation
    for(int i = 2; i < 10; i++) {
        // Bullish FVG: Gap where bar[i-1] low > bar[i+1] high (gap up)
        double prevLow = iLow(_Symbol, PERIOD_CURRENT, i - 1);
        double nextHigh = iHigh(_Symbol, PERIOD_CURRENT, i + 1);
        double bullishGapSize = prevLow - nextHigh;

        if(bullishGapSize > 0) {
            double gapPips = bullishGapSize / _Point;
            if(_Digits == 5 || _Digits == 3) gapPips /= 10;

            if(gapPips >= FVG_MinPips) {
                // Check if FVG already exists
                bool exists = false;
                datetime fvgTime = iTime(_Symbol, PERIOD_CURRENT, i);
                for(int j = 0; j < 50; j++) {
                    if(fvgArray[j].time == fvgTime) {
                        exists = true;
                        break;
                    }
                }
                if(!exists) {
                    AddFVG(fvgTime, prevLow, nextHigh, 1);
                    Print("Bullish FVG detected: ", nextHigh, " to ", prevLow, " Size: ", gapPips, " pips");
                }
            }
        }

        // Bearish FVG: Gap where bar[i-1] high < bar[i+1] low (gap down)
        double prevHigh = iHigh(_Symbol, PERIOD_CURRENT, i - 1);
        double nextLow = iLow(_Symbol, PERIOD_CURRENT, i + 1);
        double bearishGapSize = nextLow - prevHigh;

        if(bearishGapSize > 0) {
            double gapPips = bearishGapSize / _Point;
            if(_Digits == 5 || _Digits == 3) gapPips /= 10;

            if(gapPips >= FVG_MinPips) {
                // Check if FVG already exists
                bool exists = false;
                datetime fvgTime = iTime(_Symbol, PERIOD_CURRENT, i);
                for(int j = 0; j < 50; j++) {
                    if(fvgArray[j].time == fvgTime) {
                        exists = true;
                        break;
                    }
                }
                if(!exists) {
                    AddFVG(fvgTime, nextLow, prevHigh, -1);
                    Print("Bearish FVG detected: ", prevHigh, " to ", nextLow, " Size: ", gapPips, " pips");
                }
            }
        }
    }

    // Check if FVGs are filled
    double currentPrice = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;
    for(int i = 0; i < 50; i++) {
        if(fvgArray[i].time > 0 && !fvgArray[i].filled) {
            if(currentPrice >= fvgArray[i].lower && currentPrice <= fvgArray[i].upper) {
                fvgArray[i].filled = true;
                Print("FVG filled at price: ", currentPrice);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Add Fair Value Gap to array                                     |
//+------------------------------------------------------------------+
void AddFVG(datetime time, double upper, double lower, int type) {
    // Check if FVG already exists
    for(int i = 0; i < 50; i++) {
        if(fvgArray[i].time == time) return;
    }

    // Find empty slot
    int index = -1;
    for(int i = 0; i < 50; i++) {
        if(fvgArray[i].time == 0) {
            index = i;
            break;
        }
    }

    if(index == -1) {
        // Shift array
        for(int i = 0; i < 49; i++) {
            fvgArray[i] = fvgArray[i + 1];
        }
        index = 49;
    }

    fvgArray[index].time = time;
    fvgArray[index].upper = upper;
    fvgArray[index].lower = lower;
    fvgArray[index].type = type;
    fvgArray[index].filled = false;
}

//+------------------------------------------------------------------+
//| Update Order Blocks                                             |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    // Look for order blocks in recent bars
    for(int i = 1; i < OrderBlock_Lookback; i++) {
        double open = iOpen(_Symbol, PERIOD_CURRENT, i);
        double close = iClose(_Symbol, PERIOD_CURRENT, i);
        double high = iHigh(_Symbol, PERIOD_CURRENT, i);
        double low = iLow(_Symbol, PERIOD_CURRENT, i);

        double bodySize = MathAbs(close - open);
        double totalSize = high - low;
        double bodyRatio = totalSize > 0 ? bodySize / totalSize : 0;

        // Check for strong bullish candle (potential bullish order block)
        if(close > open && bodyRatio >= OrderBlock_MinBody) {
            // Check if next few bars show rejection from this level
            bool rejection = false;
            for(int j = i - 1; j >= MathMax(0, i - 3); j--) {
                if(iLow(_Symbol, PERIOD_CURRENT, j) <= high &&
                   iClose(_Symbol, PERIOD_CURRENT, j) > high) {
                    rejection = true;
                    break;
                }
            }

            if(rejection) {
                AddOrderBlock(iTime(_Symbol, PERIOD_CURRENT, i), high, low, 1);
            }
        }

        // Check for strong bearish candle (potential bearish order block)
        if(close < open && bodyRatio >= OrderBlock_MinBody) {
            // Check if next few bars show rejection from this level
            bool rejection = false;
            for(int j = i - 1; j >= MathMax(0, i - 3); j--) {
                if(iHigh(_Symbol, PERIOD_CURRENT, j) >= low &&
                   iClose(_Symbol, PERIOD_CURRENT, j) < low) {
                    rejection = true;
                    break;
                }
            }

            if(rejection) {
                AddOrderBlock(iTime(_Symbol, PERIOD_CURRENT, i), high, low, -1);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Add Order Block to array                                        |
//+------------------------------------------------------------------+
void AddOrderBlock(datetime time, double upper, double lower, int type) {
    // Check if order block already exists
    for(int i = 0; i < 50; i++) {
        if(orderBlocks[i].time == time) return;
    }

    // Find empty slot
    int index = -1;
    for(int i = 0; i < 50; i++) {
        if(orderBlocks[i].time == 0) {
            index = i;
            break;
        }
    }

    if(index == -1) {
        // Shift array
        for(int i = 0; i < 49; i++) {
            orderBlocks[i] = orderBlocks[i + 1];
        }
        index = 49;
    }

    orderBlocks[index].time = time;
    orderBlocks[index].upper = upper;
    orderBlocks[index].lower = lower;
    orderBlocks[index].type = type;
    orderBlocks[index].used = false;
}

//+------------------------------------------------------------------+
//| Check for trade setups                                          |
//+------------------------------------------------------------------+
void CheckTradeSetups() {
    // Don't trade if neutral bias
    if(trendBias == 0) return;

    // Check if we already have open trades
    if(CountOpenTrades() > 0) return;

    Print("Checking trade setups - Trend bias: ", trendBias, " Structure shift: ", structureShift);

    double currentPrice = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;

    // Look for bullish setup
    if(trendBias == 1) {
        // Check for price in bullish FVG or bullish order block
        bool inBullishZone = false;
        double entryPrice = 0;

        // Check FVGs
        for(int i = 0; i < 50; i++) {
            if(fvgArray[i].time > 0 && !fvgArray[i].filled && fvgArray[i].type == 1) {
                Print("Checking bullish FVG: ", fvgArray[i].lower, " to ", fvgArray[i].upper, " Current price: ", currentPrice);
                if(currentPrice >= fvgArray[i].lower && currentPrice <= fvgArray[i].upper) {
                    inBullishZone = true;
                    entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                    Print("Price in bullish FVG zone!");
                    break;
                }
            }
        }

        // Check Order Blocks if not in FVG
        if(!inBullishZone) {
            for(int i = 0; i < 50; i++) {
                if(orderBlocks[i].time > 0 && !orderBlocks[i].used && orderBlocks[i].type == 1) {
                    Print("Checking bullish OB: ", orderBlocks[i].lower, " to ", orderBlocks[i].upper, " Current price: ", currentPrice);
                    if(currentPrice >= orderBlocks[i].lower && currentPrice <= orderBlocks[i].upper) {
                        inBullishZone = true;
                        entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                        orderBlocks[i].used = true;
                        Print("Price in bullish OB zone!");
                        break;
                    }
                }
            }
        }

        if(inBullishZone) {
            Print("Executing buy trade at: ", entryPrice);
            ExecuteBuyTrade(entryPrice);
        }
    }

    // Look for bearish setup
    if(trendBias == -1) {
        // Check for price in bearish FVG or bearish order block
        bool inBearishZone = false;
        double entryPrice = 0;

        // Check FVGs
        for(int i = 0; i < 50; i++) {
            if(fvgArray[i].time > 0 && !fvgArray[i].filled && fvgArray[i].type == -1) {
                Print("Checking bearish FVG: ", fvgArray[i].lower, " to ", fvgArray[i].upper, " Current price: ", currentPrice);
                if(currentPrice >= fvgArray[i].lower && currentPrice <= fvgArray[i].upper) {
                    inBearishZone = true;
                    entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                    Print("Price in bearish FVG zone!");
                    break;
                }
            }
        }

        // Check Order Blocks if not in FVG
        if(!inBearishZone) {
            for(int i = 0; i < 50; i++) {
                if(orderBlocks[i].time > 0 && !orderBlocks[i].used && orderBlocks[i].type == -1) {
                    Print("Checking bearish OB: ", orderBlocks[i].lower, " to ", orderBlocks[i].upper, " Current price: ", currentPrice);
                    if(currentPrice >= orderBlocks[i].lower && currentPrice <= orderBlocks[i].upper) {
                        inBearishZone = true;
                        entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                        orderBlocks[i].used = true;
                        Print("Price in bearish OB zone!");
                        break;
                    }
                }
            }
        }

        if(inBearishZone) {
            Print("Executing sell trade at: ", entryPrice);
            ExecuteSellTrade(entryPrice);
        }
    }
}

//+------------------------------------------------------------------+
//| Execute buy trade                                               |
//+------------------------------------------------------------------+
void ExecuteBuyTrade(double entryPrice) {
    double lotSize = CalculateLotSize();
    if(lotSize <= 0) return;

    // Calculate stop loss and take profit
    SwingPoint lastLow, dummy1, dummy2, dummy3;
    GetLatestSwingPoints(dummy1, lastLow, dummy2, dummy3);

    double stopLoss = lastLow.price - (StructureBreakPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    double riskPips = (entryPrice - stopLoss) / _Point;
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;

    double takeProfit = entryPrice + (riskPips * RiskRewardRatio * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));

    // Place buy order
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = MaxSlippagePips;
    request.magic = MagicNumber;
    request.comment = "ICT Buy";

    if(OrderSend(request, result)) {
        Print("Buy order placed successfully. Ticket: ", result.order);
    } else {
        Print("Failed to place buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Execute sell trade                                              |
//+------------------------------------------------------------------+
void ExecuteSellTrade(double entryPrice) {
    double lotSize = CalculateLotSize();
    if(lotSize <= 0) return;

    // Calculate stop loss and take profit
    SwingPoint lastHigh, dummy1, dummy2, dummy3;
    GetLatestSwingPoints(lastHigh, dummy1, dummy2, dummy3);

    double stopLoss = lastHigh.price + (StructureBreakPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    double riskPips = (stopLoss - entryPrice) / _Point;
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;

    double takeProfit = entryPrice - (riskPips * RiskRewardRatio * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));

    // Place sell order
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = MaxSlippagePips;
    request.magic = MagicNumber;
    request.comment = "ICT Sell";

    if(OrderSend(request, result)) {
        Print("Sell order placed successfully. Ticket: ", result.order);
    } else {
        Print("Failed to place sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                |
//+------------------------------------------------------------------+
double CalculateLotSize() {
    if(FixedLotSize > 0) return FixedLotSize;

    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * RiskPercent / 100.0;

    // Get recent swing point for risk calculation
    SwingPoint lastHigh, lastLow, dummy1, dummy2;
    GetLatestSwingPoints(lastHigh, lastLow, dummy1, dummy2);

    double riskPips = 0;
    if(trendBias == 1 && lastLow.price > 0) {
        riskPips = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - lastLow.price) / _Point;
    } else if(trendBias == -1 && lastHigh.price > 0) {
        riskPips = (lastHigh.price - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    }

    if(_Digits == 5 || _Digits == 3) riskPips /= 10;
    if(riskPips <= 0) return 0;

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotSize = riskAmount / (riskPips * tickValue);

    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

    return lotSize;
}

//+------------------------------------------------------------------+
//| Count open trades                                               |
//+------------------------------------------------------------------+
int CountOpenTrades() {
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Manage open trades                                              |
//+------------------------------------------------------------------+
void ManageOpenTrades() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

            // Basic trailing stop or other management logic can be added here
            // For now, we rely on the SL/TP set at entry
        }
    }
}
