//+------------------------------------------------------------------+
//|                                                ICT_Strategy_EA.mq5 |
//|                                    Inner Circle Trader Strategy EA |
//|                                                                      |
//+------------------------------------------------------------------+
#property copyright "ICT Strategy EA"
#property link      ""
#property version   "1.00"
#property strict

//--- Input parameters
input group "=== Risk Management ==="
input double   RiskPercent = 2.0;           // Risk percentage per trade
input double   RiskRewardRatio = 2.0;       // Risk:Reward ratio (1:X)
input double   FixedLotSize = 0.1;          // Fixed lot size (0 = auto calculate)
input int      MaxSpreadPips = 3;           // Maximum spread in pips

input group "=== Market Structure ==="
input int      SwingLookback = 10;          // Bars to look back for swing points
input int      StructureMinBars = 5;        // Minimum bars for structure confirmation
input double   StructureBreakPips = 5.0;    // Pips needed to confirm structure break

input group "=== Smart Money Concepts ==="
input int      FVG_MinPips = 8;             // Minimum FVG size in pips
input int      OrderBlock_Lookback = 20;    // Bars to look back for order blocks
input double   OrderBlock_MinBody = 0.6;    // Minimum body ratio for order block candle

input group "=== Trade Management ==="
input int      MagicNumber = 12345;         // Magic number for trades
input int      MaxSlippagePips = 3;         // Maximum slippage in pips

//--- Global variables
struct SwingPoint {
    datetime time;
    double   price;
    int      type;  // 1 = high, -1 = low
    int      bar_index;
};

struct FairValueGap {
    datetime time;
    double   upper;
    double   lower;
    int      type;  // 1 = bullish FVG, -1 = bearish FVG
    bool     filled;
};

struct OrderBlock {
    datetime time;
    double   upper;
    double   lower;
    int      type;  // 1 = bullish OB, -1 = bearish OB
    bool     used;
};

SwingPoint swingHighs[100];
SwingPoint swingLows[100];
FairValueGap fvgArray[50];
OrderBlock orderBlocks[50];

int trendBias = 0;  // 1 = bullish, -1 = bearish, 0 = neutral
bool structureShift = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("ICT Strategy EA initialized");

    // Initialize arrays
    for(int i = 0; i < 100; i++) {
        swingHighs[i].time = 0;
        swingLows[i].time = 0;
    }

    for(int i = 0; i < 50; i++) {
        fvgArray[i].time = 0;
        orderBlocks[i].time = 0;
    }

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("ICT Strategy EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Check spread filter
    if(!CheckSpreadFilter()) return;
    
    // Update market structure
    UpdateSwingPoints();
    UpdateTrendBias();
    CheckStructureShift();
    
    // Update smart money concepts
    UpdateFairValueGaps();
    UpdateOrderBlocks();
    
    // Check for trade opportunities
    CheckTradeSetups();
    
    // Manage existing trades
    ManageOpenTrades();
}

//+------------------------------------------------------------------+
//| Check spread filter                                             |
//+------------------------------------------------------------------+
bool CheckSpreadFilter() {
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    if(_Digits == 5 || _Digits == 3) spread /= 10;

    return spread <= MaxSpreadPips;
}

//+------------------------------------------------------------------+
//| Update swing points (highs and lows)                           |
//+------------------------------------------------------------------+
void UpdateSwingPoints() {
    // Find swing highs
    for(int i = SwingLookback; i < Bars(_Symbol, PERIOD_CURRENT) - SwingLookback; i++) {
        bool isSwingHigh = true;
        double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, i);
        
        // Check if current bar is higher than surrounding bars
        for(int j = i - SwingLookback; j <= i + SwingLookback; j++) {
            if(j != i && iHigh(_Symbol, PERIOD_CURRENT, j) >= currentHigh) {
                isSwingHigh = false;
                break;
            }
        }
        
        if(isSwingHigh) {
            // Add to swing highs array
            AddSwingPoint(swingHighs, iTime(_Symbol, PERIOD_CURRENT, i), currentHigh, 1, i);
        }
    }
    
    // Find swing lows
    for(int i = SwingLookback; i < Bars(_Symbol, PERIOD_CURRENT) - SwingLookback; i++) {
        bool isSwingLow = true;
        double currentLow = iLow(_Symbol, PERIOD_CURRENT, i);
        
        // Check if current bar is lower than surrounding bars
        for(int j = i - SwingLookback; j <= i + SwingLookback; j++) {
            if(j != i && iLow(_Symbol, PERIOD_CURRENT, j) <= currentLow) {
                isSwingLow = false;
                break;
            }
        }
        
        if(isSwingLow) {
            // Add to swing lows array
            AddSwingPoint(swingLows, iTime(_Symbol, PERIOD_CURRENT, i), currentLow, -1, i);
        }
    }
}

//+------------------------------------------------------------------+
//| Add swing point to array                                        |
//+------------------------------------------------------------------+
void AddSwingPoint(SwingPoint &array[], datetime time, double price, int type, int bar_index) {
    // Find empty slot or oldest entry
    int index = -1;
    int arraySize = (type == 1) ? 100 : 100; // Both arrays have size 100

    for(int i = 0; i < arraySize; i++) {
        if(array[i].time == 0) {
            index = i;
            break;
        }
    }

    if(index == -1) {
        // Shift array and use last position
        for(int i = 0; i < arraySize - 1; i++) {
            array[i] = array[i + 1];
        }
        index = arraySize - 1;
    }

    array[index].time = time;
    array[index].price = price;
    array[index].type = type;
    array[index].bar_index = bar_index;
}

//+------------------------------------------------------------------+
//| Update trend bias based on market structure                     |
//+------------------------------------------------------------------+
void UpdateTrendBias() {
    // Get latest swing points
    SwingPoint lastHigh, lastLow;
    SwingPoint prevHigh, prevLow;

    GetLatestSwingPoints(lastHigh, lastLow, prevHigh, prevLow);

    if(lastHigh.time == 0 || lastLow.time == 0 || prevHigh.time == 0 || prevLow.time == 0) {
        trendBias = 0;
        return;
    }

    // Determine trend bias
    if(lastHigh.price > prevHigh.price && lastLow.price > prevLow.price) {
        trendBias = 1;  // Bullish (Higher Highs, Higher Lows)
    }
    else if(lastHigh.price < prevHigh.price && lastLow.price < prevLow.price) {
        trendBias = -1; // Bearish (Lower Highs, Lower Lows)
    }
    else {
        trendBias = 0;  // Neutral/Ranging
    }
}

//+------------------------------------------------------------------+
//| Get latest swing points                                         |
//+------------------------------------------------------------------+
void GetLatestSwingPoints(SwingPoint &lastHigh, SwingPoint &lastLow, SwingPoint &prevHigh, SwingPoint &prevLow) {
    // Initialize
    lastHigh.time = 0; lastLow.time = 0; prevHigh.time = 0; prevLow.time = 0;

    // Find two most recent swing highs
    int highCount = 0;
    for(int i = 99; i >= 0 && highCount < 2; i--) {
        if(swingHighs[i].time > 0) {
            if(highCount == 0) lastHigh = swingHighs[i];
            else if(highCount == 1) prevHigh = swingHighs[i];
            highCount++;
        }
    }

    // Find two most recent swing lows
    int lowCount = 0;
    for(int i = 99; i >= 0 && lowCount < 2; i--) {
        if(swingLows[i].time > 0) {
            if(lowCount == 0) lastLow = swingLows[i];
            else if(lowCount == 1) prevLow = swingLows[i];
            lowCount++;
        }
    }
}

//+------------------------------------------------------------------+
//| Check for market structure shift                                |
//+------------------------------------------------------------------+
void CheckStructureShift() {
    structureShift = false;

    if(trendBias == 0) return;

    double currentPrice = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;
    SwingPoint lastHigh, lastLow, prevHigh, prevLow;
    GetLatestSwingPoints(lastHigh, lastLow, prevHigh, prevLow);

    if(lastHigh.time == 0 || lastLow.time == 0) return;

    double breakDistance = StructureBreakPips * _Point;
    if(_Digits == 5 || _Digits == 3) breakDistance *= 10;

    // Check for bullish structure shift (break of previous high)
    if(trendBias == -1 && currentPrice > lastHigh.price + breakDistance) {
        structureShift = true;
        trendBias = 1;
    }
    // Check for bearish structure shift (break of previous low)
    else if(trendBias == 1 && currentPrice < lastLow.price - breakDistance) {
        structureShift = true;
        trendBias = -1;
    }
}

//+------------------------------------------------------------------+
//| Update Fair Value Gaps                                          |
//+------------------------------------------------------------------+
void UpdateFairValueGaps() {
    // Check last few bars for FVG formation
    for(int i = 1; i < 10; i++) {
        // Bullish FVG: Gap between bar[i+1] high and bar[i-1] low
        double gap1_high = iHigh(_Symbol, PERIOD_CURRENT, i + 1);
        double gap1_low = iLow(_Symbol, PERIOD_CURRENT, i - 1);
        double gap1_size = gap1_low - gap1_high;

        if(gap1_size > 0) {
            double gapPips = gap1_size / _Point;
            if(_Digits == 5 || _Digits == 3) gapPips /= 10;

            if(gapPips >= FVG_MinPips) {
                // Add bullish FVG
                AddFVG(iTime(_Symbol, PERIOD_CURRENT, i), gap1_low, gap1_high, 1);
            }
        }

        // Bearish FVG: Gap between bar[i-1] high and bar[i+1] low
        double gap2_high = iHigh(_Symbol, PERIOD_CURRENT, i - 1);
        double gap2_low = iLow(_Symbol, PERIOD_CURRENT, i + 1);
        double gap2_size = gap2_high - gap2_low;

        if(gap2_size > 0) {
            double gapPips = gap2_size / _Point;
            if(_Digits == 5 || _Digits == 3) gapPips /= 10;

            if(gapPips >= FVG_MinPips) {
                // Add bearish FVG
                AddFVG(iTime(_Symbol, PERIOD_CURRENT, i), gap2_high, gap2_low, -1);
            }
        }
    }

    // Check if FVGs are filled
    double currentPrice = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;
    for(int i = 0; i < 50; i++) {
        if(fvgArray[i].time > 0 && !fvgArray[i].filled) {
            if(currentPrice >= fvgArray[i].lower && currentPrice <= fvgArray[i].upper) {
                fvgArray[i].filled = true;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Add Fair Value Gap to array                                     |
//+------------------------------------------------------------------+
void AddFVG(datetime time, double upper, double lower, int type) {
    // Check if FVG already exists
    for(int i = 0; i < 50; i++) {
        if(fvgArray[i].time == time) return;
    }

    // Find empty slot
    int index = -1;
    for(int i = 0; i < 50; i++) {
        if(fvgArray[i].time == 0) {
            index = i;
            break;
        }
    }

    if(index == -1) {
        // Shift array
        for(int i = 0; i < 49; i++) {
            fvgArray[i] = fvgArray[i + 1];
        }
        index = 49;
    }

    fvgArray[index].time = time;
    fvgArray[index].upper = upper;
    fvgArray[index].lower = lower;
    fvgArray[index].type = type;
    fvgArray[index].filled = false;
}

//+------------------------------------------------------------------+
//| Update Order Blocks                                             |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    // Look for order blocks in recent bars
    for(int i = 1; i < OrderBlock_Lookback; i++) {
        double open = iOpen(_Symbol, PERIOD_CURRENT, i);
        double close = iClose(_Symbol, PERIOD_CURRENT, i);
        double high = iHigh(_Symbol, PERIOD_CURRENT, i);
        double low = iLow(_Symbol, PERIOD_CURRENT, i);

        double bodySize = MathAbs(close - open);
        double totalSize = high - low;
        double bodyRatio = totalSize > 0 ? bodySize / totalSize : 0;

        // Check for strong bullish candle (potential bullish order block)
        if(close > open && bodyRatio >= OrderBlock_MinBody) {
            // Check if next few bars show rejection from this level
            bool rejection = false;
            for(int j = i - 1; j >= MathMax(0, i - 3); j--) {
                if(iLow(_Symbol, PERIOD_CURRENT, j) <= high &&
                   iClose(_Symbol, PERIOD_CURRENT, j) > high) {
                    rejection = true;
                    break;
                }
            }

            if(rejection) {
                AddOrderBlock(iTime(_Symbol, PERIOD_CURRENT, i), high, low, 1);
            }
        }

        // Check for strong bearish candle (potential bearish order block)
        if(close < open && bodyRatio >= OrderBlock_MinBody) {
            // Check if next few bars show rejection from this level
            bool rejection = false;
            for(int j = i - 1; j >= MathMax(0, i - 3); j--) {
                if(iHigh(_Symbol, PERIOD_CURRENT, j) >= low &&
                   iClose(_Symbol, PERIOD_CURRENT, j) < low) {
                    rejection = true;
                    break;
                }
            }

            if(rejection) {
                AddOrderBlock(iTime(_Symbol, PERIOD_CURRENT, i), high, low, -1);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Add Order Block to array                                        |
//+------------------------------------------------------------------+
void AddOrderBlock(datetime time, double upper, double lower, int type) {
    // Check if order block already exists
    for(int i = 0; i < 50; i++) {
        if(orderBlocks[i].time == time) return;
    }

    // Find empty slot
    int index = -1;
    for(int i = 0; i < 50; i++) {
        if(orderBlocks[i].time == 0) {
            index = i;
            break;
        }
    }

    if(index == -1) {
        // Shift array
        for(int i = 0; i < 49; i++) {
            orderBlocks[i] = orderBlocks[i + 1];
        }
        index = 49;
    }

    orderBlocks[index].time = time;
    orderBlocks[index].upper = upper;
    orderBlocks[index].lower = lower;
    orderBlocks[index].type = type;
    orderBlocks[index].used = false;
}

//+------------------------------------------------------------------+
//| Check for trade setups                                          |
//+------------------------------------------------------------------+
void CheckTradeSetups() {
    // Don't trade if no structure shift or neutral bias
    if(!structureShift || trendBias == 0) return;

    // Check if we already have open trades
    if(CountOpenTrades() > 0) return;

    double currentPrice = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;

    // Look for bullish setup
    if(trendBias == 1) {
        // Check for price in bullish FVG or bullish order block
        bool inBullishZone = false;
        double entryPrice = 0;

        // Check FVGs
        for(int i = 0; i < 50; i++) {
            if(fvgArray[i].time > 0 && !fvgArray[i].filled && fvgArray[i].type == 1) {
                if(currentPrice >= fvgArray[i].lower && currentPrice <= fvgArray[i].upper) {
                    inBullishZone = true;
                    entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                    break;
                }
            }
        }

        // Check Order Blocks if not in FVG
        if(!inBullishZone) {
            for(int i = 0; i < 50; i++) {
                if(orderBlocks[i].time > 0 && !orderBlocks[i].used && orderBlocks[i].type == 1) {
                    if(currentPrice >= orderBlocks[i].lower && currentPrice <= orderBlocks[i].upper) {
                        inBullishZone = true;
                        entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                        orderBlocks[i].used = true;
                        break;
                    }
                }
            }
        }

        if(inBullishZone) {
            ExecuteBuyTrade(entryPrice);
        }
    }

    // Look for bearish setup
    if(trendBias == -1) {
        // Check for price in bearish FVG or bearish order block
        bool inBearishZone = false;
        double entryPrice = 0;

        // Check FVGs
        for(int i = 0; i < 50; i++) {
            if(fvgArray[i].time > 0 && !fvgArray[i].filled && fvgArray[i].type == -1) {
                if(currentPrice >= fvgArray[i].lower && currentPrice <= fvgArray[i].upper) {
                    inBearishZone = true;
                    entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                    break;
                }
            }
        }

        // Check Order Blocks if not in FVG
        if(!inBearishZone) {
            for(int i = 0; i < 50; i++) {
                if(orderBlocks[i].time > 0 && !orderBlocks[i].used && orderBlocks[i].type == -1) {
                    if(currentPrice >= orderBlocks[i].lower && currentPrice <= orderBlocks[i].upper) {
                        inBearishZone = true;
                        entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                        orderBlocks[i].used = true;
                        break;
                    }
                }
            }
        }

        if(inBearishZone) {
            ExecuteSellTrade(entryPrice);
        }
    }
}

//+------------------------------------------------------------------+
//| Execute buy trade                                               |
//+------------------------------------------------------------------+
void ExecuteBuyTrade(double entryPrice) {
    double lotSize = CalculateLotSize();
    if(lotSize <= 0) return;

    // Calculate stop loss and take profit
    SwingPoint lastLow, dummy1, dummy2, dummy3;
    GetLatestSwingPoints(dummy1, lastLow, dummy2, dummy3);

    double stopLoss = lastLow.price - (StructureBreakPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    double riskPips = (entryPrice - stopLoss) / _Point;
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;

    double takeProfit = entryPrice + (riskPips * RiskRewardRatio * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));

    // Place buy order
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = MaxSlippagePips;
    request.magic = MagicNumber;
    request.comment = "ICT Buy";

    if(OrderSend(request, result)) {
        Print("Buy order placed successfully. Ticket: ", result.order);
    } else {
        Print("Failed to place buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Execute sell trade                                              |
//+------------------------------------------------------------------+
void ExecuteSellTrade(double entryPrice) {
    double lotSize = CalculateLotSize();
    if(lotSize <= 0) return;

    // Calculate stop loss and take profit
    SwingPoint lastHigh, dummy1, dummy2, dummy3;
    GetLatestSwingPoints(lastHigh, dummy1, dummy2, dummy3);

    double stopLoss = lastHigh.price + (StructureBreakPips * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));
    double riskPips = (stopLoss - entryPrice) / _Point;
    if(_Digits == 5 || _Digits == 3) riskPips /= 10;

    double takeProfit = entryPrice - (riskPips * RiskRewardRatio * _Point * (_Digits == 5 || _Digits == 3 ? 10 : 1));

    // Place sell order
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = entryPrice;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = MaxSlippagePips;
    request.magic = MagicNumber;
    request.comment = "ICT Sell";

    if(OrderSend(request, result)) {
        Print("Sell order placed successfully. Ticket: ", result.order);
    } else {
        Print("Failed to place sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                |
//+------------------------------------------------------------------+
double CalculateLotSize() {
    if(FixedLotSize > 0) return FixedLotSize;

    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * RiskPercent / 100.0;

    // Get recent swing point for risk calculation
    SwingPoint lastHigh, lastLow, dummy1, dummy2;
    GetLatestSwingPoints(lastHigh, lastLow, dummy1, dummy2);

    double riskPips = 0;
    if(trendBias == 1 && lastLow.price > 0) {
        riskPips = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - lastLow.price) / _Point;
    } else if(trendBias == -1 && lastHigh.price > 0) {
        riskPips = (lastHigh.price - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    }

    if(_Digits == 5 || _Digits == 3) riskPips /= 10;
    if(riskPips <= 0) return 0;

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotSize = riskAmount / (riskPips * tickValue);

    // Normalize lot size
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

    return lotSize;
}

//+------------------------------------------------------------------+
//| Count open trades                                               |
//+------------------------------------------------------------------+
int CountOpenTrades() {
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Manage open trades                                              |
//+------------------------------------------------------------------+
void ManageOpenTrades() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

            // Basic trailing stop or other management logic can be added here
            // For now, we rely on the SL/TP set at entry
        }
    }
}
