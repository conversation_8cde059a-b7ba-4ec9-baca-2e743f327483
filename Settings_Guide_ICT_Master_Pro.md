# 🎛️ ICT Master Pro EA - Settings Guide

## 📁 **AVAILABLE SETTING FILES**

I've created 5 optimized setting files for different trading styles:

### 1. 🛡️ **ICT_Conservative_Settings.set**
- **Best for**: Beginners, small accounts, risk-averse traders
- **Risk**: 1% per trade, max 3% daily
- **Trades**: 2-4 per day
- **Expected**: 60-70% win rate, 3-8% monthly return
- **Features**: High confirmation requirements, tight risk control

### 2. ⚖️ **ICT_Balanced_Settings.set**
- **Best for**: Intermediate traders, medium accounts
- **Risk**: 1.5% per trade, max 5% daily
- **Trades**: 3-6 per day
- **Expected**: 65-75% win rate, 5-12% monthly return
- **Features**: Balanced risk/reward, moderate frequency

### 3. 🚀 **ICT_Aggressive_Settings.set**
- **Best for**: Experienced traders, larger accounts
- **Risk**: 2% per trade, max 8% daily
- **Trades**: 5-8 per day
- **Expected**: 60-70% win rate, 8-20% monthly return
- **Features**: Higher frequency, more opportunities

### 4. ⚡ **ICT_Scalping_Settings.set**
- **Best for**: M15/M30 timeframes, quick trades
- **Risk**: 0.75% per trade, max 6% daily
- **Trades**: 8-15 per day
- **Expected**: 55-65% win rate, frequent small profits
- **Features**: Fast execution, tight stops

### 5. 🏆 **ICT_PropFirm_Settings.set**
- **Best for**: Prop firm challenges, funded accounts
- **Risk**: 0.5% per trade, max 2% daily
- **Trades**: 2-4 per day
- **Expected**: 70-80% win rate, steady growth
- **Features**: Ultra-conservative, high win rate focus

---

## 📥 **HOW TO LOAD SETTINGS**

### **Method 1: Direct File Load**
1. Copy the `.set` file to your MetaTrader 5 folder:
   ```
   C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\[TerminalID]\MQL5\Presets\
   ```

2. Right-click on the EA in Navigator
3. Select "Attach to a chart"
4. Click "Load" button in the inputs tab
5. Select your desired `.set` file
6. Click "OK"

### **Method 2: Manual Input**
1. Attach EA to chart
2. Copy settings from the `.set` file
3. Paste values into the input parameters
4. Click "OK"

---

## 🎯 **RECOMMENDED SETTINGS BY ACCOUNT SIZE**

### 💰 **Small Account ($500-$2,000)**
- **Use**: ICT_Conservative_Settings.set
- **Timeframe**: H1 or H4
- **Pairs**: EURUSD, GBPUSD only
- **Focus**: Capital preservation, learning

### 💰 **Medium Account ($2,000-$10,000)**
- **Use**: ICT_Balanced_Settings.set
- **Timeframe**: H1
- **Pairs**: Major pairs (EUR, GBP, USD, JPY)
- **Focus**: Steady growth, skill development

### 💰 **Large Account ($10,000+)**
- **Use**: ICT_Aggressive_Settings.set
- **Timeframe**: H1 or M30
- **Pairs**: All majors + some minors
- **Focus**: Maximum returns, diversification

### 🏆 **Prop Firm Account**
- **Use**: ICT_PropFirm_Settings.set
- **Timeframe**: H4 or Daily
- **Pairs**: 2-3 major pairs maximum
- **Focus**: Consistency, low drawdown

---

## ⏰ **TIMEFRAME RECOMMENDATIONS**

### 📊 **H4 (4 Hour) - RECOMMENDED**
- **Best settings**: Conservative or PropFirm
- **Pros**: High quality signals, less noise
- **Cons**: Fewer trading opportunities
- **Ideal for**: Beginners, prop firms, part-time traders

### 📊 **H1 (1 Hour) - POPULAR**
- **Best settings**: Balanced or Aggressive
- **Pros**: Good balance of quality and quantity
- **Cons**: Requires more monitoring
- **Ideal for**: Most traders, full-time trading

### 📊 **M30 (30 Minutes) - ADVANCED**
- **Best settings**: Scalping or Aggressive
- **Pros**: More opportunities, faster results
- **Cons**: More noise, requires experience
- **Ideal for**: Experienced traders, scalpers

---

## 🎛️ **KEY PARAMETER EXPLANATIONS**

### 💰 **Risk Management**
```
RiskPercent: % of account risked per trade
RiskRewardRatio: Target profit vs risk (1:2 = 2.0)
MaxRiskPerDay: Maximum daily risk exposure
MaxTradesPerDay: Daily trade limit
```

### 📊 **Market Structure**
```
SwingLookback: Bars to analyze for swing points
StructureBreakPips: Confirmation distance for breaks
UseMultiTimeframe: Enable higher timeframe analysis
RequireHTFConfirmation: Strict HTF trend alignment
```

### 🧠 **Smart Money Concepts**
```
FVG_MinPips: Minimum gap size for FVG detection
OrderBlock_Lookback: Bars to scan for order blocks
OrderBlock_MinBody: Minimum candle body ratio
UseLiquidityZones: Enable liquidity detection
```

---

## 🔧 **CUSTOMIZATION TIPS**

### 🎯 **For More Trades**
- Decrease `FVG_MinPips` (8-10)
- Decrease `SwingLookback` (8-10)
- Increase `MaxTradesPerDay` (8-10)
- Set `RequireHTFConfirmation = false`

### 🛡️ **For Safer Trading**
- Increase `RiskRewardRatio` (3.0-4.0)
- Decrease `RiskPercent` (0.5-1.0%)
- Decrease `MaxRiskPerDay` (2-3%)
- Set `RequireHTFConfirmation = true`

### ⚡ **For Faster Execution**
- Use M30 or M15 timeframes
- Decrease `TrailingStopPips` (8-12)
- Increase `MaxConcurrentTrades` (3-4)
- Set `AvoidNews = false`

---

## 📊 **PERFORMANCE EXPECTATIONS**

### 🎯 **Conservative Settings**
- **Monthly Return**: 3-8%
- **Win Rate**: 60-70%
- **Max Drawdown**: 5-10%
- **Trades/Month**: 40-80

### ⚖️ **Balanced Settings**
- **Monthly Return**: 5-12%
- **Win Rate**: 65-75%
- **Max Drawdown**: 8-15%
- **Trades/Month**: 60-120

### 🚀 **Aggressive Settings**
- **Monthly Return**: 8-20%
- **Win Rate**: 60-70%
- **Max Drawdown**: 12-20%
- **Trades/Month**: 100-200

---

## ⚠️ **IMPORTANT NOTES**

### 🔍 **Before Going Live**
1. **Backtest** all settings on historical data
2. **Forward test** on demo account for 1-2 weeks
3. **Start small** with minimum risk settings
4. **Monitor closely** for first week of live trading

### 📈 **Optimization Tips**
- Test settings on your specific broker
- Adjust for your trading timezone
- Consider your internet connection stability
- Account for broker-specific spreads

### 🛡️ **Risk Warnings**
- Never risk more than you can afford to lose
- Start with conservative settings
- Monitor daily risk limits closely
- Use VPS for 24/7 operation

---

## 🎯 **QUICK START RECOMMENDATION**

### 👶 **If You're New to ICT/EAs:**
1. Use **ICT_Conservative_Settings.set**
2. Trade on **H4 timeframe**
3. Start with **EURUSD only**
4. Enable **debug mode** initially
5. Monitor for **1 week** before adjusting

### 🚀 **If You're Experienced:**
1. Use **ICT_Balanced_Settings.set**
2. Trade on **H1 timeframe**
3. Use **2-3 major pairs**
4. Disable debug mode
5. Optimize based on results

**Choose the setting file that matches your experience level and risk tolerance!** 🎯
